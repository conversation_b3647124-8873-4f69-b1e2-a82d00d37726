import { useCallback, useState } from "react"
import { ComponentRules, CSSClassesTable, UsageGuidelines } from "@/components"
import { Checkbox, Typography } from "@apollo/ui"
import {
  ArgTypes,
  Description,
  Primary,
  Source,
  Stories,
  Subtitle,
  Title,
} from "@storybook/addon-docs/blocks"
import type { <PERSON><PERSON>, StoryObj } from "@storybook/react"

/**
 * Checkbox component
 *
 * The Checkbox component provides a styled, accessible checkbox with support for
 * checked, unchecked, indeterminate states, labels, and disabled functionality.
 *
 * Notes:
 * - Default state is unchecked;
 * - Supports indeterminate state for partial selections;
 * - Labels can be ReactNode for rich content;
 * - Built on top of Base UI for accessibility.
 */
const meta = {
  title: "@apollo∕ui/Components/Inputs/Checkbox",
  component: Checkbox,
  tags: ["autodocs"],
  parameters: {
    layout: "centered",
    design: {
      type: "figma",
      url: "https://www.figma.com/design/gdmbYIRxMhNlIe0oNtSprm/%F0%9F%92%99-Apollo-Alias-Foundations-and-Styles?node-id=2210-18192&m=dev",
    },
    docs: {
      description: {
        component:
          "The Checkbox component renders a checkbox with Apollo design system styling. Supports checked, unchecked, and indeterminate states with optional labels.",
      },
      page: () => (
        <>
          <Title />
          <Subtitle />
          <Description />
          <Primary />
          <h3>Import</h3>
          <Source
            code={`import { Checkbox } from "@apollo/ui"`}
            language="tsx"
          />
          <h2 id="checkbox-props">Props</h2>
          <ArgTypes />
          <h2 id="checkbox-usage">Best Practices</h2>
          <UsageGuidelines
            guidelines={[
              "Use checkboxes to: Select one or more options from a list",
              "Use checkboxes independently from each other: selecting one checkbox shouldn’t change the selection status of another checkbox in the list. The exception is when a checkbox is used to make a bulk selection.",
              "Keep a positive tone of voice. For example: “Turn on notifications” instead of “Turn off notifications”.",
              "Use indeterminate state for parent checkboxes when some but not all children are selected",
              "Group related checkboxes together with clear section headings",
              "Ensure sufficient spacing between checkboxes for easy interaction. For example: 24px",
              "Use disabled state for options that are temporarily unavailable",
              "Consider the reading order when positioning labels relative to checkboxes",
              "Checkbox will always appear with a label."
            ]}
          />
          <h2 id="checkbox-accessibility">Accessibility</h2>
          <UsageGuidelines
            guidelines={[
              <>Always provide a visible <code>label</code> prop to ensure the checkbox's purpose is clear to all users.</>,
              <>
                Use the <code>disabled</code> prop to disable checkboxes that
                are not currently actionable, ensuring they are not focusable.
              </>,
              "Ensure sufficient color contrast between checkbox states and background in all conditions.",
              <>
                Use the <code>indeterminate</code> state appropriately for
                parent/child checkbox relationships.
              </>,
            ]}
          />
          <h2 id="checkbox-css-classes">CSS Classes</h2>
          <CSSClassesTable
            description="The Checkbox component provides several CSS classes that can be used for custom styling. These classes follow the Apollo design system naming conventions and provide access to different parts and states of the component."
            data={[
              {
                cssClassName: ".ApolloCheckbox-root",
                description: "Styles applied to checkbox",
                usageNotes: "Use for overall component positioning and spacing",
              },
              {
                cssClassName: ".ApolloCheckbox-checkbox",
                description: "Styles applied to the checkbox input element",
                usageNotes: "Target for checkbox-specific styling (size, border, background)",
              },
              {
                cssClassName: ".ApolloCheckbox-label",
                description: "Styles applied to the label text element",
                usageNotes: "Use for label typography, color, and spacing customization",
              },
            ]}
          />


          <h2 id="checkbox-examples">Examples</h2>
          <Stories title="" />
          <h2 id="checkbox-dos-donts">Do's and Don'ts</h2>
          <ComponentRules
            rules={[
              {
                positive: {
                  component: (
                    <div
                      style={{
                        display: "flex",
                        flexDirection: "column",
                        gap: 8,
                      }}
                    >
                      <Checkbox label="Enable email notifications" />
                      <Checkbox label="Enable push notifications" />
                      <Checkbox label="Enable SMS notifications" />
                    </div>
                  ),
                  description:
                    "Use clear, specific labels that describe the action or setting",
                },
                negative: {
                  component: (
                    <div
                      style={{
                        display: "flex",
                        flexDirection: "column",
                        gap: 8,
                      }}
                    >
                      <Checkbox label="Option 1" />
                      <Checkbox label="Option 2" />
                      <Checkbox label="Option 3" />
                    </div>
                  ),
                  description:
                    "Avoid generic labels that don't explain what the checkbox does",
                },
              },
              {
                positive: {
                  component: (
                    <div
                      style={{
                        display: "flex",
                        flexDirection: "column",
                        gap: 8,
                      }}
                    >
                      <Checkbox label="Select all items" indeterminate />
                      <div
                        style={{
                          paddingLeft: 24,
                          display: "flex",
                          flexDirection: "column",
                          gap: 4,
                        }}
                      >
                        <Checkbox label="Item 1" checked />
                        <Checkbox label="Item 2" />
                        <Checkbox label="Item 3" checked />
                      </div>
                    </div>
                  ),
                  description:
                    "Use indeterminate state for parent checkboxes when some children are selected",
                },
                negative: {
                  component: (
                    <div
                      style={{
                        display: "flex",
                        flexDirection: "column",
                        gap: 8,
                      }}
                    >
                      <Checkbox label="Select all items" />
                      <div
                        style={{
                          paddingLeft: 24,
                          display: "flex",
                          flexDirection: "column",
                          gap: 4,
                        }}
                      >
                        <Checkbox label="Item 1" checked />
                        <Checkbox label="Item 2" />
                        <Checkbox label="Item 3" checked />
                      </div>
                    </div>
                  ),
                  description:
                    "Don't use unchecked state for parent when some children are selected",
                },
              },
              {
                positive: {
                  component: (
                    <div
                      style={{
                        display: "flex",
                        flexDirection: "column",
                        gap: 24,
                      }}
                    >
                      <Checkbox label="I agree to the terms and conditions" />
                      <Checkbox label="Subscribe to newsletter" />
                    </div>
                  ),
                  description:
                    "Provide adequate spacing between checkboxes for easy interaction",
                },
                negative: {
                  component: (
                    <div
                      style={{
                        display: "flex",
                        flexDirection: "column",
                        gap: 2,
                      }}
                    >
                      <Checkbox label="I agree to the terms and conditions" />
                      <Checkbox label="Subscribe to newsletter" />
                    </div>
                  ),
                  description:
                    "Don't place checkboxes too close together - it makes them hard to interact with",
                },
              },
            ]}
          />
        </>
      ),
    },
  },
  argTypes: {
    label: {
      control: { type: "text" },
      description:
        "The label content for the checkbox. Can be a string or ReactNode.",
      table: { type: { summary: "ReactNode" } },
    },
    checked: {
      control: { type: "boolean" },
      description: "Controls the checked state of the checkbox.",
      table: { type: { summary: "boolean" } },
    },
    defaultChecked: {
      control: { type: "boolean" },
      description: "The default checked state (uncontrolled).",
      table: { type: { summary: "boolean" } },
    },
    indeterminate: {
      control: { type: "boolean" },
      description: "Shows the checkbox in an indeterminate state.",
      table: { type: { summary: "boolean" } },
    },
    disabled: {
      control: { type: "boolean" },
      description: "Disables the checkbox.",
      table: { type: { summary: "boolean" } },
    },
    activeLabel: {
      control: { type: "boolean" },
      description: "Whether the label is interactive.",
      table: { type: { summary: "boolean" } },
    },
    onChange: {
      control: false,
      description: "Callback fired when the checkbox state changes.",
      table: {
        type: {
          summary: "(event: Event, checked: boolean) => void",
        },
      },
    },
    ref: {
      control: false,
      description: "Ref for the underlying input element.",
      table: { type: { summary: "Ref<HTMLInputElement>" } },
    },
    rootRef: {
      control: false,
      description: "Ref for the root label element.",
      table: { type: { summary: "Ref<HTMLLabelElement>" } },
    },
    wrapperRef: {
      control: false,
      description: "Ref for the wrapper button element.",
      table: { type: { summary: "Ref<HTMLButtonElement>" } },
    },
    className: {
      control: { type: "text" },
      description: "Additional CSS class names to apply to the root element.",
      table: { type: { summary: "string" } },
    },
  },
  args: {
    label: "Checkbox",
    defaultChecked: false,
  },
} satisfies Meta<typeof Checkbox>

export default meta

type Story = StoryObj<typeof Checkbox>

/** Default Checkbox (demonstrates basic functionality) */
export const Overview: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "Overview Checkbox with default settings. The component defaults to unchecked state with an optional label.",
      },
    },
  },
  args: {
    label: "Default Checkbox",
  },
}

/** Checkbox labels */
export const Labels: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "Checkboxes without labels for use in tables or compact layouts.",
      },
    },
  },
  render: (args) => (
    <div style={{ display: "flex", gap: 16, alignItems: "center" }}>
      <Checkbox {...args} />
      <Checkbox {...args} checked />
      <Checkbox {...args} indeterminate />
    </div>
  ),
}


/** Checkbox with custom labels */
export const CustomLabels: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "Checkboxes with custom ReactNode labels including styled content.",
      },
    },
  },
  render: (args) => (
    <div
      style={{
        display: "flex",
        flexDirection: "column",
        gap: 16,
        alignItems: "flex-start",
      }}
    >
      <Checkbox {...args} label="Simple text label" />
      <Checkbox
        {...args}
        label={
          <span style={{ color: "var(--apl-alias-color-primary-primary)", fontWeight: "600" }}>
            Styled label
          </span>
        }
      />
      <Checkbox
        {...args}
        label={
          <div>
            <div style={{ fontWeight: "600" }}>Multi-line label</div>
            <div style={{ fontSize: "14px", color: "#6b7280" }}>
              With description text
            </div>
          </div>
        }
      />
      <Checkbox
        {...args}
        label={
          <>
            I agree to the{" "}
            <a
              href="https://example.com/terms"
              style={{ color: "var(--apl-alias-color-primary-primary)", textDecoration: "underline" }}
            >
              terms and conditions
            </a>
          </>
        }
      />
    </div>
  ),
}

/** Checkbox disabled states */
export const Disabled: Story = {
  parameters: {
    docs: {
      description: {
        story: "Checkboxes in disabled state across all possible states.",
      },
    },
  },
  render: () => (
    <div
      style={{
        display: "flex",
        gap: 24,
        alignItems: "center",
        flexWrap: "wrap",
      }}
    >
      <Checkbox disabled label="Disabled Unchecked" />
      <Checkbox disabled checked label="Disabled Checked" />
      <Checkbox disabled indeterminate label="Disabled Indeterminate" />
      <Checkbox disabled activeLabel label="Disabled Checked with active label" />
    </div>
  ),
}

/** Comprehensive states showcase */
export const States: Story = {
  parameters: {
    layout: "padded",
    docs: {
      description: {
        story:
          "A comprehensive showcase of all checkbox states including default, checked, indeterminate, and disabled variations.",
      },
    },
  },
  render: () => {
    return (
      <div
        style={{
          display: "grid",
          gridTemplateColumns: "repeat(auto-fit, minmax(200px, 1fr))",
          gap: 20,
          alignItems: "flex-start",
        }}
      >
        <div style={{ display: "flex", flexDirection: "column", gap: 12 }}>
          <Typography
            level="bodyLarge"
            style={{ fontWeight: "600", marginBottom: 8 }}
          >
            Default States
          </Typography>
          <Checkbox label="Unchecked" />
          <Checkbox label="Checked" checked />
          <Checkbox label="Indeterminate" indeterminate />
        </div>

        <div style={{ display: "flex", flexDirection: "column", gap: 12 }}>
          <Typography
            level="bodyLarge"
            style={{ fontWeight: "600", marginBottom: 8 }}
          >
            Disabled States
          </Typography>
          <Checkbox label="Disabled Unchecked" disabled />
          <Checkbox label="Disabled Checked" checked disabled />
          <Checkbox label="Disabled Indeterminate" indeterminate disabled />
        </div>

        <div style={{ display: "flex", flexDirection: "column", gap: 12 }}>
          <Typography
            level="bodyLarge"
            style={{ fontWeight: "600", marginBottom: 8 }}
          >
            Interactive Examples
          </Typography>
          <Checkbox label="Terms & Conditions" />
          <Checkbox label="Newsletter Subscription" />
          <Checkbox label="Marketing Emails" checked />
          <Checkbox label="SMS Notifications" disabled />
        </div>
      </div>
    )
  },
}

/** Controlled checkbox example */
export const Controlled: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "A controlled checkbox that manages its own state with React hooks.",
      },
    },
  },
  render: () => {
    function ControlledDemo() {
      const [checked, setChecked] = useState(false)

      const handleChange = useCallback((_event: Event, isChecked: boolean) => {
        setChecked(isChecked)
      }, [])

      return (
        <div
          style={{
            display: "flex",
            flexDirection: "column",
            gap: 16,
            alignItems: "flex-start",
          }}
        >
          <Checkbox
            label={`Controlled Checkbox (${checked ? "Checked" : "Unchecked"})`}
            checked={checked}
            onChange={handleChange}
          />
          <Typography level="bodySmall" style={{ color: "#6b7280" }}>
            Current state: {checked ? "✓ Checked" : "○ Unchecked"}
          </Typography>
        </div>
      )
    }
    return <ControlledDemo />
  },
}

/** Indeterminate parent-child relationship */
export const IndeterminateGroup: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "Demonstrates indeterminate state in parent-child checkbox relationships.",
      },
    },
  },
  render: () => {
    function IndeterminateDemo() {
      const items = [
        { id: "item-1", label: "Item 1", checked: false },
        { id: "item-2", label: "Item 2", checked: true },
        { id: "item-3", label: "Item 3", checked: false },
      ]
      const [checkedItems, setCheckedItems] = useState(items)

      const allChecked = checkedItems.every((item) => item.checked)
      const isIndeterminate =
        checkedItems.some((item) => item.checked) && !allChecked

      const handleParentChange = useCallback(
        (_event: Event, checked: boolean) => {
          setCheckedItems((prev) => prev.map((item) => ({ ...item, checked })))
        },
        []
      )

      const handleChildChange = useCallback((id: string) => {
        setCheckedItems((prev) =>
          prev.map((item) =>
            item.id === id ? { ...item, checked: !item.checked } : item
          )
        )
      }, [])

      return (
        <div
          style={{
            display: "flex",
            flexDirection: "column",
            gap: 12,
            alignItems: "flex-start",
          }}
        >
          <Checkbox
            label="Select All Items"
            checked={allChecked}
            indeterminate={isIndeterminate}
            onChange={handleParentChange}
          />
          <div
            style={{
              paddingLeft: 24,
              display: "flex",
              flexDirection: "column",
              gap: 8,
            }}
          >
            {checkedItems.map((item) => (
              <Checkbox
                key={item.id}
                label={item.label}
                checked={item.checked}
                onChange={() => handleChildChange(item.id)}
              />
            ))}
          </div>
          <Typography
            level="bodySmall"
            style={{ color: "#6b7280", marginTop: 8 }}
          >
            Selected: {checkedItems.filter((item) => item.checked).length} of{" "}
            {checkedItems.length} items
          </Typography>
        </div>
      )
    }
    return <IndeterminateDemo />
  },
}



/** Form integration example */
export const FormIntegration: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "Example of checkboxes integrated into a form with various use cases.",
      },
    },
  },
  render: () => {
    function FormDemo() {
      const [preferences, setPreferences] = useState({
        emailNotifications: true,
        pushNotifications: false,
        smsNotifications: false,
        marketingEmails: false,
        newsletter: true,
        terms: false,
      })

      const handlePreferenceChange = useCallback(
        (key: keyof typeof preferences) => {
          return (_event: Event, checked: boolean) => {
            setPreferences((prev) => ({ ...prev, [key]: checked }))
          }
        },
        []
      )

      return (
        <div
          style={{
            display: "flex",
            flexDirection: "column",
            gap: 24,
            maxWidth: 400,
          }}
        >
          <div>
            <Typography
              level="bodyLarge"
              style={{ fontWeight: "600", marginBottom: 12 }}
            >
              Notification Preferences
            </Typography>
            <div style={{ display: "flex", flexDirection: "column", gap: 8 }}>
              <Checkbox
                label="Email notifications"
                checked={preferences.emailNotifications}
                onChange={handlePreferenceChange("emailNotifications")}
              />
              <Checkbox
                label="Push notifications"
                checked={preferences.pushNotifications}
                onChange={handlePreferenceChange("pushNotifications")}
              />
              <Checkbox
                label="SMS notifications"
                checked={preferences.smsNotifications}
                onChange={handlePreferenceChange("smsNotifications")}
              />
            </div>
          </div>

          <div>
            <Typography
              level="bodyLarge"
              style={{ fontWeight: "600", marginBottom: 12 }}
            >
              Marketing Preferences
            </Typography>
            <div style={{ display: "flex", flexDirection: "column", gap: 8 }}>
              <Checkbox
                label="Marketing emails"
                checked={preferences.marketingEmails}
                onChange={handlePreferenceChange("marketingEmails")}
              />
              <Checkbox
                label="Newsletter subscription"
                checked={preferences.newsletter}
                onChange={handlePreferenceChange("newsletter")}
              />
            </div>
          </div>

          <div style={{ borderTop: "1px solid #e5e7eb", paddingTop: 16 }}>
            <Checkbox
              label="I agree to the terms and conditions"
              checked={preferences.terms}
              onChange={handlePreferenceChange("terms")}
            />
          </div>

          <Typography
            level="bodySmall"
            style={{ color: "#6b7280", marginTop: 8 }}
          >
            Selected preferences:{" "}
            {Object.values(preferences).filter(Boolean).length} of{" "}
            {Object.keys(preferences).length}
          </Typography>
        </div>
      )
    }
    return <FormDemo />
  },
}
